import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:money_mouthy_two/screens/sign_up.dart';
import 'package:money_mouthy_two/screens/forgot_password.dart';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:money_mouthy_two/controllers/profile_controller.dart';
import 'dart:async';
import 'dart:io';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  bool _isLoading = false;
  bool _showPasswordField = false;
  String? _errorMessage;

  // Add FirebaseAuth instance for convenience
  final FirebaseAuth _auth = FirebaseAuth.instance;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // Check network connectivity - try multiple hosts for better reliability
  Future<bool> _hasNetworkConnection() async {
    final hosts = ['google.com', '*******', 'firebase.google.com'];

    for (String host in hosts) {
      try {
        final result = await InternetAddress.lookup(
          host,
        ).timeout(const Duration(seconds: 10));
        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
          return true;
        }
      } catch (_) {
        continue; // Try next host
      }
    }
    return false; // All hosts failed
  }

  void _showBlockedUserDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.block, color: Colors.red, size: 24),
              SizedBox(width: 8),
              Text('Account Blocked'),
            ],
          ),
          content: const Text(
            'Your account has been blocked. Please contact support for assistance.',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.pushReplacementNamed(context, '/login');
              },
              child: const Text('Back to Login'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.pushNamed(context, '/support');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0f172a),
              ),
              child: const Text(
                'Contact Support',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleInitialSubmit() async {
    final input = _emailController.text.trim();
    if (input.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter your email or username';
      });
      return;
    }

    // Check for admin email redirect
    if (input == '<EMAIL>') {
      Get.offAllNamed('/admin/login');
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Simple delay to simulate checking - no network check needed here
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        setState(() {
          _isLoading = false;
          _showPasswordField = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'An error occurred. Please try again.';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _login() async {
    if (_emailController.text.trim().isEmpty ||
        _passwordController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = 'Please enter both email and password';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Add timeout to Firebase authentication - increased timeout
      final credential = await _auth
          .signInWithEmailAndPassword(
            email: _emailController.text.trim(),
            password: _passwordController.text.trim(),
          )
          .timeout(const Duration(seconds: 30));

      // Reload user to get the latest emailVerified flag with timeout
      await credential.user?.reload().timeout(const Duration(seconds: 20));
      final user = _auth.currentUser;

      if (user == null) {
        throw FirebaseAuthException(
          code: 'user-not-found',
          message: 'User account not found',
        );
      }

      // Email verification check commented out - users can proceed without verification
      // if (!user.emailVerified) {
      //   // Send a new verification email with timeout
      //   await user.sendEmailVerification().timeout(const Duration(seconds: 20));
      //   await _auth.signOut();

      //   setState(() {
      //     _errorMessage =
      //         'Please verify your email first. A new verification link has been sent to your email.';
      //     _isLoading = false;
      //   });
      //   return;
      // }

      // Update or create user document (emailVerified/lastLogin) with timeout
      try {
        await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
          'emailVerified': true,
          'lastLogin': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true)).timeout(const Duration(seconds: 20));
      } catch (e) {
        debugPrint('Unable to update user doc after login: $e');
      }

      // Retrieve user document once with timeout
      Map<String, dynamic>? userData;
      bool profileCompleted = false;
      try {
        final snapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get()
            .timeout(const Duration(seconds: 20));
        userData = snapshot.data();
        profileCompleted = (userData?['profileCompleted'] ?? false) as bool;
      } catch (e) {
        debugPrint('Unable to fetch user profileCompleted flag: $e');
      }

      if (!mounted) return;

      // Check if user is blocked
      final userStatus = userData?['status'] ?? 'active';
      if (userStatus == 'blocked') {
        // Sign out the user immediately
        await _auth.signOut();

        if (!mounted) return;

        // Show blocked user dialog
        _showBlockedUserDialog();
        return;
      }

      // route decision with enhanced profile completion check
      final hasUsername =
          (userData?['username']?.toString().isNotEmpty ?? false);
      final hasName = userData?['name']?.toString().isNotEmpty ?? false;
      // Email verification check removed since it's no longer required

      // Enhanced profile completion check with fallback logic (email verification not required)
      final isActuallyComplete = profileCompleted || (hasUsername && hasName);

      // Initialize ProfileController with current user data after successful login
      try {
        await ProfileController.instance.initializeCurrentUser();
        debugPrint('ProfileController initialized after login');
      } catch (e) {
        debugPrint('Error initializing ProfileController after login: $e');
      }

      if (!mounted) return;

      if (isActuallyComplete) {
        Navigator.pushReplacementNamed(context, '/home');
      } else if (!hasUsername) {
        Navigator.pushReplacementNamed(context, '/choose_username');
      } else {
        Navigator.pushReplacementNamed(context, '/create_profile');
      }
    } on TimeoutException {
      if (mounted) {
        // Check network connectivity only when there's a timeout
        final hasConnection = await _hasNetworkConnection();
        final errorMessage = hasConnection
            ? 'Request timed out. Firebase servers might be slow. Please try again.'
            : 'No internet connection. Please check your network and try again.';

        setState(() {
          _errorMessage = errorMessage;
          _isLoading = false;
        });
      }
    } on FirebaseAuthException catch (e) {
      if (mounted) {
        String errorMessage;
        switch (e.code) {
          case 'user-not-found':
            errorMessage =
                'No account found with this email. Please sign up first.';
            break;
          case 'wrong-password':
          case 'invalid-credential':
            errorMessage = 'Incorrect email or password. Please try again.';
            break;
          case 'invalid-email':
            errorMessage = 'Please enter a valid email address.';
            break;
          case 'user-disabled':
            errorMessage =
                'This account has been disabled. Please contact support.';
            break;
          case 'network-request-failed':
            errorMessage =
                'Network error. Please check your connection and try again.';
            break;
          default:
            errorMessage = e.message ?? 'Login failed. Please try again.';
        }
        setState(() {
          _errorMessage = errorMessage;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'An unexpected error occurred. Please try again.';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: kIsWeb ? 800 : double.infinity,
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  const SizedBox(height: 40),

                  // Title
                  const Center(
                    child: Text(
                      'Log In',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // Tagline
                  RichText(
                    text: const TextSpan(
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      children: <TextSpan>[
                        TextSpan(text: 'Your opinion has minimum value '),
                        TextSpan(
                          text: '\$0.05',
                          style: TextStyle(
                            color: Color(0xFF5159FF),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextSpan(text: ' or more! Join the conversation'),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 40),

                  // Social login buttons (if not showing password field)
                  if (!_showPasswordField) ...[
                    // // Google login button
                    // ElevatedButton(
                    //   onPressed: () {},
                    //   style: ElevatedButton.styleFrom(
                    //     backgroundColor: Colors.white,
                    //     foregroundColor: Colors.black,
                    //     elevation: 0,
                    //     side: BorderSide(color: Colors.grey.shade300, width: 1),
                    //     shape: RoundedRectangleBorder(
                    //       borderRadius: BorderRadius.circular(12),
                    //     ),
                    //     padding: const EdgeInsets.symmetric(vertical: 16),
                    //     minimumSize: const Size(double.infinity, 56),
                    //   ),
                    //   child: Row(
                    //     mainAxisAlignment: MainAxisAlignment.center,
                    //     children: [
                    //       SizedBox(
                    //         width: 24,
                    //         height: 24,
                    //         child: ClipOval(
                    //           child: Image.asset(
                    //             'assets/images/google.png',
                    //             fit: BoxFit.cover,
                    //           ),
                    //         ),
                    //       ),
                    //       const SizedBox(width: 12),
                    //       const Text(
                    //         "Continue with Google",
                    //         style: TextStyle(
                    //           fontSize: 16,
                    //           fontWeight: FontWeight.w500,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),

                    // const SizedBox(height: 16),

                    // // Apple login button
                    // ElevatedButton(
                    //   onPressed: () {},
                    //   style: ElevatedButton.styleFrom(
                    //     backgroundColor: Colors.white,
                    //     foregroundColor: Colors.black,
                    //     elevation: 0,
                    //     side: BorderSide(color: Colors.grey.shade300, width: 1),
                    //     shape: RoundedRectangleBorder(
                    //       borderRadius: BorderRadius.circular(12),
                    //     ),
                    //     padding: const EdgeInsets.symmetric(vertical: 16),
                    //     minimumSize: const Size(double.infinity, 56),
                    //   ),
                    //   child: Row(
                    //     mainAxisAlignment: MainAxisAlignment.center,
                    //     children: [
                    //       const Icon(Icons.apple, size: 24),
                    //       const SizedBox(width: 12),
                    //       const Text(
                    //         "Continue with Apple",
                    //         style: TextStyle(
                    //           fontSize: 16,
                    //           fontWeight: FontWeight.w500,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),

                    // const SizedBox(height: 32),

                    // // Or divider
                    // const Row(
                    //   children: [
                    //     Expanded(
                    //       child: Divider(color: Colors.grey, thickness: 0.5),
                    //     ),
                    //     Padding(
                    //       padding: EdgeInsets.symmetric(horizontal: 16.0),
                    //       child: Text(
                    //         "Or",
                    //         style: TextStyle(color: Colors.grey),
                    //       ),
                    //     ),
                    //     Expanded(
                    //       child: Divider(color: Colors.grey, thickness: 0.5),
                    //     ),
                    //   ],
                    // ),

                    // const SizedBox(height: 32),
                  ],

                  // Email/Username field
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_showPasswordField) ...[
                        Text(
                          _emailController.text.contains('@')
                              ? _emailController.text
                              : '@${_emailController.text}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                      if (!_showPasswordField)
                        TextField(
                          controller: _emailController,
                          decoration: InputDecoration(
                            hintText: 'Enter email or username',
                            hintStyle: const TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                            ),
                            filled: true,
                            fillColor: Colors.grey[50],
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(
                                color: Color(0xFF5159FF),
                                width: 2,
                              ),
                            ),
                            errorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(
                                color: Colors.red,
                                width: 2,
                              ),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                          ),
                          style: const TextStyle(fontSize: 16),
                        ),
                      if (_showPasswordField)
                        TextField(
                          controller: _passwordController,
                          obscureText: _obscurePassword,
                          decoration: InputDecoration(
                            hintText: 'Enter your password',
                            hintStyle: const TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                            ),
                            filled: true,
                            fillColor: Colors.grey[50],
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(
                                color: Color(0xFF5159FF),
                                width: 2,
                              ),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscurePassword
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: Colors.grey,
                              ),
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                            ),
                          ),
                          style: const TextStyle(fontSize: 16),
                        ),
                    ],
                  ),

                  // Error message
                  if (_errorMessage != null) ...[
                    const SizedBox(height: 8),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red, fontSize: 14),
                      ),
                    ),
                  ],

                  const SizedBox(height: 24),

                  // Login button
                  ElevatedButton(
                    onPressed: _isLoading
                        ? null
                        : _showPasswordField
                            ? _login
                            : _handleInitialSubmit,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF5159FF),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      minimumSize: const Size(double.infinity, 56),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text(
                            'Log In',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),

                  const SizedBox(height: 16),

                  // Forgot Password link
                  TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ForgotPasswordScreen(),
                        ),
                      );
                    },
                    child: const Text(
                      'Forgot Password?',
                      style: TextStyle(
                        color: Color(0xFF5159FF),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                  // Create New Account button
                  TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SignUpScreen(),
                        ),
                      );
                    },
                    child: const Text(
                      'Create a new account',
                      style: TextStyle(
                        color: Color(0xFF5159FF),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Terms and conditions
                  Padding(
                    padding: const EdgeInsets.only(bottom: 40),
                    child: RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        children: [
                          TextSpan(
                            text: "By signing up you agree to Money Mouthy's ",
                          ),
                          TextSpan(
                            text: "terms and conditions",
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Navigator.pushNamed(context, '/terms');
                              },
                          ),
                          TextSpan(text: "."),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
